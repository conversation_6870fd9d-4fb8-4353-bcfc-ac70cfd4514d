# Python Dependencies for TimesheetPro

## Core Django Dependencies
Django>=4.2.0,<5.0
django-extensions>=3.2.0
python-decouple>=3.6

## Database
psycopg2-binary>=2.9.0  # PostgreSQL (optional)

## Excel Export
openpyxl>=3.1.0

## PDF Export (Optional)
reportlab>=4.0.0
Pillow>=10.0.0

## Authentication & Security
django-crispy-forms>=2.0
crispy-bootstrap5>=0.7

## Development Dependencies
django-debug-toolbar>=4.0.0  # Development only
black>=23.0.0                 # Code formatting
flake8>=6.0.0                # Linting
pytest-django>=4.5.0         # Testing

## Production Dependencies
gunicorn>=21.0.0             # WSGI server
whitenoise>=6.5.0            # Static file serving
django-environ>=0.10.0       # Environment variables

## Optional Enhancements
django-compressor>=4.3       # CSS/JS compression
django-redis>=5.3.0          # Redis caching
celery>=5.3.0                # Background tasks

## Installation Commands

### Basic Setup
```bash
pip install Django>=4.2.0
pip install openpyxl>=3.1.0
pip install python-decouple>=3.6
```

### Full Development Setup
```bash
pip install -r requirements.txt
```

### Production Setup
```bash
pip install -r requirements.txt --no-dev
```

## Version Compatibility

### Python Version
- Python 3.9+
- Python 3.10+ (recommended)
- Python 3.11+ (latest)

### Django Version
- Django 4.2 LTS (recommended)
- Django 5.0+ (latest)

## Package Management

### Requirements Files Structure
```
requirements/
├── base.txt           # Common dependencies
├── development.txt    # Development only
├── production.txt     # Production only
└── testing.txt        # Testing dependencies
```

### Lock File Generation
```bash
pip freeze > requirements.txt
```

### Environment-Specific Installation
```bash
# Development
pip install -r requirements/development.txt

# Production
pip install -r requirements/production.txt
```
