# Strict Style Workflow

## Code Formatting Standards

### Python Code Style (PEP 8)

#### Line Length
- Maximum 88 characters per line (Black standard)
- Break long lines at logical points

#### Imports
```python
# Standard library imports
import os
import sys
from datetime import datetime, date

# Third-party imports
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required

# Local imports
from .models import TimeEntry
from .utils import generate_excel_timesheet
```

#### Function and Class Naming
```python
# Functions: snake_case
def timesheet_dashboard(request):
    pass

def add_entry(request):
    pass

# Classes: PascalCase
class TimeEntry(models.Model):
    pass

# Constants: UPPER_SNAKE_CASE
MAX_HOURS_PER_DAY = 24
DEFAULT_DATE_FORMAT = '%Y-%m-%d'
```

#### Variable Naming
```python
# Variables: snake_case
monthly_total = 0
user_entries = []
time_hours = Decimal('8.00')

# Private variables: leading underscore
_internal_cache = {}
```

### Django-Specific Conventions

#### Model Fields
```python
class TimeEntry(models.Model):
    # Foreign keys: descriptive names
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Date/time fields: clear naming
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Text fields: descriptive and length-limited
    activity = models.CharField(max_length=200)
    description = models.TextField()
```

#### View Functions
```python
@login_required
def timesheet_dashboard(request):
    """Main dashboard showing monthly timesheet summary"""
    # Get parameters with defaults
    today = date.today()
    month = int(request.GET.get('month', today.month))
    year = int(request.GET.get('year', today.year))
    
    # Query database efficiently
    entries = TimeEntry.objects.filter(
        user=request.user,
        date__year=year,
        date__month=month
    ).order_by('date')
    
    # Build context dictionary
    context = {
        'entries': entries,
        'month': month,
        'year': year,
    }
    
    return render(request, 'timesheets/dashboard.html', context)
```

#### URL Patterns
```python
urlpatterns = [
    path('', views.timesheet_dashboard, name='dashboard'),
    path('add/', views.add_entry, name='add_entry'),
    path('edit/<int:entry_id>/', views.edit_entry, name='edit_entry'),
]
```

### Template Conventions

#### HTML Structure
```html
{% extends 'base.html' %}
{% load static %}

{% block title %}Timesheet Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto p-6">
    <!-- Clear semantic structure -->
    <header class="mb-6">
        <h1 class="text-3xl font-bold">Dashboard</h1>
    </header>
    
    <main>
        <!-- Content here -->
    </main>
</div>
{% endblock %}
```

#### DaisyUI Class Usage
```html
<!-- Consistent component usage -->
<button class="btn btn-primary">Primary Action</button>
<button class="btn btn-secondary">Secondary Action</button>
<button class="btn btn-outline">Outline Button</button>

<!-- Table styling -->
<table class="table table-zebra">
    <thead>
        <tr>
            <th>Column Header</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Table Data</td>
        </tr>
    </tbody>
</table>
```

### File Organization

#### Directory Structure
```
timesheets/
├── models.py          # Data models only
├── views.py           # View functions only
├── urls.py            # URL patterns only
├── utils.py           # Utility functions
├── forms.py           # Django forms (if used)
├── admin.py           # Admin configuration
├── apps.py            # App configuration
└── migrations/        # Database migrations
```

#### Template Structure
```
templates/timesheets/
├── base.html          # Base template
├── dashboard.html     # Main dashboard
├── add_entry.html     # Add/edit form
└── confirm_delete.html # Delete confirmation
```

### Documentation Standards

#### Docstrings
```python
def timesheet_dashboard(request):
    """
    Display monthly timesheet dashboard.
    
    Shows summary of time entries for selected month with
    navigation controls and action buttons.
    
    Args:
        request: HTTP request object
        
    Returns:
        Rendered dashboard template with context
    """
```

#### Comments
```python
# Calculate weekly totals for summary display
weekly_totals = {}
for entry in entries:
    week = entry.week_display
    if week not in weekly_totals:
        weekly_totals[week] = Decimal('0.00')
    weekly_totals[week] += entry.time_hours
```

### Error Handling

#### View Error Handling
```python
@login_required
def add_entry(request):
    if request.method == 'POST':
        try:
            # Process form data
            entry = TimeEntry.objects.create(...)
            messages.success(request, 'Entry added successfully!')
            return redirect('timesheets:dashboard')
        except ValidationError as e:
            messages.error(request, f'Validation error: {e}')
        except Exception as e:
            messages.error(request, f'Unexpected error: {e}')
            # Log error for debugging
            logger.error(f'Error adding entry: {e}', exc_info=True)
    
    return render(request, 'timesheets/add_entry.html', context)
```

### Testing Standards

#### Test Function Naming
```python
def test_dashboard_displays_current_month():
    """Test dashboard shows current month by default"""
    pass

def test_add_entry_creates_time_entry():
    """Test adding entry creates TimeEntry object"""
    pass
```

### Code Review Checklist

- [ ] Code follows PEP 8 standards
- [ ] Functions have docstrings
- [ ] Variables have descriptive names
- [ ] No magic numbers or strings
- [ ] Error handling is present
- [ ] Templates use semantic HTML
- [ ] DaisyUI classes used consistently
- [ ] No code duplication
- [ ] Tests cover new functionality
