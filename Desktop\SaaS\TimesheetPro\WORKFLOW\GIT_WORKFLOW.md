# Git Workflow for TimesheetPro

## Branch Strategy

### Main Branches
- `main` - Production ready code
- `develop` - Development integration branch
- `feature/*` - Feature development branches

## Daily Workflow

### Starting New Feature
```bash
# Switch to develop branch
git checkout develop

# Pull latest changes
git pull origin develop

# Create new feature branch
git checkout -b feature/timesheet-dashboard

# Work on feature...
```

### Committing Changes
```bash
# Stage changes
git add .

# Commit with descriptive message
git commit -m "Add dashboard monthly navigation"

# Push to remote
git push origin feature/timesheet-dashboard
```

### Merging Feature
```bash
# Switch to develop
git checkout develop

# Pull latest
git pull origin develop

# Merge feature
git merge feature/timesheet-dashboard

# Push changes
git push origin develop

# Delete feature branch
git branch -d feature/timesheet-dashboard
git push origin --delete feature/timesheet-dashboard
```

## Commit Message Convention

### Format
```
type(scope): description

[optional body]

[optional footer]
```

### Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

### Examples
```bash
git commit -m "feat(dashboard): add monthly timesheet navigation"
git commit -m "fix(forms): validate time hours input"
git commit -m "docs(readme): update installation instructions"
```

## Release Workflow

### Preparing Release
```bash
# Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v1.0.0

# Update version numbers, changelog
# Test thoroughly

# Merge to main
git checkout main
git merge release/v1.0.0
git tag v1.0.0

# Merge back to develop
git checkout develop
git merge release/v1.0.0

# Push everything
git push origin main
git push origin develop
git push origin v1.0.0
```

## Best Practices

### Commit Guidelines
- Make atomic commits
- Write clear commit messages
- Don't commit sensitive data
- Test before committing

### Branch Guidelines
- Keep branches focused
- Delete merged branches
- Use descriptive branch names
- Regular pulls from develop

### Code Review
- Review before merging
- Test functionality
- Check code style
- Verify documentation
