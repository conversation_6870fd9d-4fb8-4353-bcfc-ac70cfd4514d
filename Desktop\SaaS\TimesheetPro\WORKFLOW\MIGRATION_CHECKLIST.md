# Migration Checklist

## Pre-Migration Steps

### 1. Backup Database
```bash
# SQLite backup
cp db.sqlite3 db_backup_$(date +%Y%m%d_%H%M%S).sqlite3

# PostgreSQL backup
pg_dump your_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. Review Model Changes
- [ ] Check field types and constraints
- [ ] Verify foreign key relationships
- [ ] Review default values
- [ ] Check for data loss potential

### 3. Create Migration
```bash
# Generate migration
python manage.py makemigrations timesheets

# Review migration file
cat timesheets/migrations/0001_initial.py
```

## Migration Execution

### 1. Dry Run
```bash
# Check what migrations will run
python manage.py showmigrations

# Plan migration execution
python manage.py migrate --plan
```

### 2. Execute Migration
```bash
# Run migrations
python manage.py migrate

# Verify migration status
python manage.py showmigrations
```

### 3. Data Migration (if needed)
```python
# Create data migration
python manage.py makemigrations --empty timesheets

# Edit migration file to add data operations
```

## Post-Migration Verification

### 1. Database Integrity
```bash
# Check database structure
python manage.py dbshell
.schema timesheets_timeentry  # SQLite
\d timesheets_timeentry       # PostgreSQL
```

### 2. Application Testing
- [ ] Test model creation
- [ ] Test model updates
- [ ] Test model deletion
- [ ] Test relationships
- [ ] Test constraints

### 3. Data Verification
- [ ] Check existing data integrity
- [ ] Verify new fields have correct defaults
- [ ] Test calculated properties
- [ ] Verify user data isolation

## Rollback Plan

### 1. Migration Rollback
```bash
# Rollback to specific migration
python manage.py migrate timesheets 0001

# Rollback all migrations for app
python manage.py migrate timesheets zero
```

### 2. Database Restore
```bash
# Restore from backup if needed
cp db_backup_YYYYMMDD_HHMMSS.sqlite3 db.sqlite3

# Restart application
python manage.py runserver
```

## Common Issues & Solutions

### Migration Conflicts
```bash
# Merge conflicting migrations
python manage.py makemigrations --merge
```

### Fake Migrations
```bash
# Mark migration as applied without running
python manage.py migrate --fake timesheets 0001
```

### Reset Migrations
```bash
# Remove migration files (be careful!)
rm timesheets/migrations/0*.py

# Recreate migrations
python manage.py makemigrations timesheets
```

## Best Practices

- Always backup before migrations
- Test migrations on development first
- Review migration files before applying
- Keep migrations atomic and reversible
- Document complex data migrations
- Monitor application after migration
